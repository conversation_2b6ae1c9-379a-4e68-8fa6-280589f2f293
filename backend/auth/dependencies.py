"""
Authentication dependencies for FastAPI with HttpOnly cookie support
"""
from typing import Optional
from fastapi import Depends, HTTPException, status, Request
from backend.utils.security import verify_token
from backend.services.auth_service import auth_service
from backend.schemas.auth import UserResponse


async def get_current_user(request: Request) -> UserResponse:
    """
    Dependency to get current authenticated user from HttpOnly cookies

    Args:
        request: FastAPI request object containing cookies

    Returns:
        UserResponse: Current user information

    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
    )

    # Get access token from HttpOnly cookie
    access_token = request.cookies.get("access_token")
    if not access_token:
        raise credentials_exception

    # Verify token
    payload = verify_token(access_token, "access")
    if payload is None:
        raise credentials_exception

    user_id = payload.get("sub")
    if user_id is None:
        raise credentials_exception

    try:
        user_id = int(user_id)
    except ValueError:
        raise credentials_exception

    # Get user from database
    user = await auth_service.get_current_user(user_id)
    if user is None:
        raise credentials_exception

    return user


async def get_current_user_optional(request: Request) -> Optional[UserResponse]:
    """
    Dependency to get current authenticated user from cookies (optional)

    Args:
        request: FastAPI request object containing cookies

    Returns:
        Optional[UserResponse]: Current user information or None
    """
    try:
        return await get_current_user(request)
    except HTTPException:
        return None
