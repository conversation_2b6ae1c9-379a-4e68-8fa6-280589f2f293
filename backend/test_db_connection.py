"""
Test database connection script
"""
import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from prisma import Prisma
from backend.config.settings import settings

async def test_connection():
    """Test database connection"""
    print("Testing database connection...")
    print(f"Project root: {project_root}")
    print(f"Current working directory: {Path.cwd()}")
    print(f"Database URL from settings: {settings.database_url}")

    # Check if database file exists
    if settings.database_url.startswith("file:"):
        db_path = settings.database_url.replace("file:", "")
        db_file = Path(db_path)
        print(f"Database file path: {db_file}")
        print(f"Database file exists: {db_file.exists()}")
        print(f"Database file absolute path: {db_file.absolute()}")

        if db_file.exists():
            print(f"Database file size: {db_file.stat().st_size} bytes")
        else:
            print("❌ Database file does not exist!")
            print("Checking alternative paths...")

            # Check if file exists in project root
            alt_path = project_root / "prisma" / "dev.db"
            print(f"Alternative path: {alt_path}")
            print(f"Alternative path exists: {alt_path.exists()}")

            return False

    # Test Prisma connection
    db = Prisma()
    try:
        print("Connecting to database...")
        await db.connect()
        print("✅ Successfully connected to database!")

        # Test a simple query
        print("Testing query...")
        users = await db.user.find_many()
        print(f"✅ Query successful! Found {len(users)} users in database")

        await db.disconnect()
        print("✅ Database connection test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    if not success:
        sys.exit(1)
