"""
Data routes for fetching application data
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional
from backend.auth.dependencies import get_current_user
from backend.schemas.auth import UserResponse
from backend.services.auth_service import auth_service
import alog

# Create router
router = APIRouter(prefix="/data", tags=["Data"])


@router.get("/users")
async def get_users(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    current_user: UserResponse = Depends(get_current_user)
):
    """
    Get paginated list of users (protected endpoint)

    Args:
        page: Page number (starts from 1)
        limit: Number of users per page (max 100)
        current_user: Current authenticated user

    Returns:
        Paginated list of users
    """
    try:
        # Connect to database
        await auth_service.connect_db()

        # Calculate offset
        offset = (page - 1) * limit

        # Get total count
        total_users = await auth_service.db.user.count()

        # Get paginated users
        users = await auth_service.db.user.find_many(
            skip=offset,
            take=limit,
            order_by={'createdAt': 'desc'}
        )

        # Convert to response format
        user_list = []
        for user in users:
            user_list.append({
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "created_at": user.createdAt.isoformat(),
                "updated_at": user.updatedAt.isoformat()
            })

        # Calculate pagination info
        total_pages = (total_users + limit - 1) // limit
        has_next = page < total_pages
        has_prev = page > 1

        response = {
            "users": user_list,
            "total": total_users,
            "page": page,
            "limit": limit,
            "total_pages": total_pages,
            "has_next": has_next,
            "has_prev": has_prev
        }

        alog.info(f"Retrieved {len(user_list)} users for page {page} (user: {current_user.email})")
        return response

    except Exception as e:
        alog.error(f"Error fetching users: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch users"
        )
    finally:
        await auth_service.disconnect_db()


@router.get("/users/{user_id}")
async def get_user(
    user_id: int,
    current_user: UserResponse = Depends(get_current_user)
):
    """
    Get specific user by ID (protected endpoint)

    Args:
        user_id: ID of the user to fetch
        current_user: Current authenticated user

    Returns:
        User information
    """
    try:
        # Connect to database
        await auth_service.connect_db()

        # Get user
        user = await auth_service.db.user.find_unique(
            where={"id": user_id}
        )

        if not user:
            raise HTTPException(
                status_code=404,
                detail="User not found"
            )

        response = {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "created_at": user.createdAt.isoformat(),
            "updated_at": user.updatedAt.isoformat()
        }

        alog.info(f"Retrieved user {user_id} (requested by: {current_user.email})")
        return response

    except HTTPException:
        raise
    except Exception as e:
        alog.error(f"Error fetching user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch user"
        )
    finally:
        await auth_service.disconnect_db()


@router.get("/stats")
async def get_stats(
    current_user: UserResponse = Depends(get_current_user)
):
    """
    Get application statistics (protected endpoint)

    Args:
        current_user: Current authenticated user

    Returns:
        Application statistics
    """
    try:
        # Connect to database
        await auth_service.connect_db()

        # Get various counts
        total_users = await auth_service.db.user.count()

        # You can add more statistics here as your app grows
        # total_conversations = await auth_service.db.conversation.count()
        # total_messages = await auth_service.db.message.count()

        response = {
            "total_users": total_users,
            "current_user_id": current_user.id,
            # "total_conversations": total_conversations,
            # "total_messages": total_messages,
        }

        alog.info(f"Retrieved stats (requested by: {current_user.email})")
        return response

    except Exception as e:
        alog.error(f"Error fetching stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch statistics"
        )
    finally:
        await auth_service.disconnect_db()


# Health check endpoint
@router.get("/health")
async def health_check():
    """
    Health check endpoint for data service

    Returns:
        dict: Health status
    """
    return {"status": "healthy", "service": "data"}
