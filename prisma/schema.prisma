// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        Int     @id @default(autoincrement())
  email     String  @unique
  name      String?
  password  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  conversations Conversation[] @relation("ConversationUsers", references: [id])
  messages  Message[]
}

model Conversation {
  id        Int     @id @default(autoincrement())
  name      String?
  users     User[]  @relation("ConversationUsers", references: [id])
  messages  Message[]
}

model Message {
  id        Int     @id @default(autoincrement())
  content   String
  sender    User    @relation(fields: [senderId], references: [id])
  senderId  Int     @relation(fields: [senderId], references: [id])
  conversation Conversation @relation(fields: [conversationId], references: [id])
  conversationId Int @relation(fields: [conversationId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
