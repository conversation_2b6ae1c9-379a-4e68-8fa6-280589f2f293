# Centralized Axios API Client Usage Guide

This document explains how to use the centralized axios API client in the frontend application.

## Overview

The centralized API client (`/src/utils/api.ts`) provides:
- Automatic authentication header injection
- Consistent error handling
- Request/response logging in development
- Automatic token refresh on 401 errors
- Type-safe API calls with TypeScript

## Basic Usage

### Import the API Client

```typescript
import { apiClient, ApiError } from '@/utils/api';
```

### Making API Calls

#### GET Request
```typescript
// Simple GET request
const users = await apiClient.get<User[]>('/data/users');

// GET with query parameters
const paginatedUsers = await apiClient.get<UsersResponse>(
  `/data/users?page=${page}&limit=${limit}`
);
```

#### POST Request
```typescript
// Create a new resource
const newUser = await apiClient.post<User>('/auth/signup', {
  email: '<EMAIL>',
  name: '<PERSON>',
  password: 'securepassword123'
});

// Login
const authResponse = await apiClient.post<AuthResponse>('/auth/signin', {
  email: '<EMAIL>',
  password: 'password123'
});
```

#### PUT Request
```typescript
// Update a resource
const updatedUser = await apiClient.put<User>(`/users/${userId}`, {
  name: 'Updated Name',
  email: '<EMAIL>'
});
```

#### DELETE Request
```typescript
// Delete a resource
await apiClient.delete(`/users/${userId}`);
```

## Error Handling

The API client automatically handles common errors and provides consistent error objects:

```typescript
try {
  const users = await apiClient.get<User[]>('/data/users');
  setUsers(users);
} catch (error) {
  const apiError = error as ApiError;
  setError(apiError.message);
  console.error('API Error:', apiError);
}
```

### Error Types

```typescript
interface ApiError {
  message: string;    // User-friendly error message
  detail?: string;    // Additional error details
  success: boolean;   // Always false for errors
}
```

## Authentication

### Automatic Token Injection

The API client automatically:
1. Reads JWT tokens from localStorage
2. Adds `Authorization: Bearer <token>` header to all requests
3. Handles 401 errors by clearing tokens and redirecting to login

### Manual Token Management

```typescript
// Tokens are automatically managed by AuthContext
// No manual token handling required in most cases

// The API client reads from localStorage:
// - Key: 'auth_tokens'
// - Value: { access_token: string, refresh_token: string, token_type: string }
```

## React Hook Examples

### Custom Hook for API Calls

```typescript
import { useState, useEffect } from 'react';
import { apiClient, ApiError } from '@/utils/api';

function useUsers(page: number = 1, limit: number = 10) {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await apiClient.get<UsersResponse>(
          `/data/users?page=${page}&limit=${limit}`
        );
        
        setUsers(response.users);
      } catch (err) {
        const apiError = err as ApiError;
        setError(apiError.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [page, limit]);

  return { users, loading, error };
}
```

### Component Usage

```typescript
import React from 'react';
import { apiClient } from '@/utils/api';

function UsersList() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get<UsersResponse>('/data/users');
      setUsers(response.users);
    } catch (error) {
      console.error('Failed to fetch users:', error);
    } finally {
      setLoading(false);
    }
  };

  const createUser = async (userData: CreateUserRequest) => {
    try {
      const newUser = await apiClient.post<User>('/users', userData);
      setUsers(prev => [...prev, newUser]);
    } catch (error) {
      console.error('Failed to create user:', error);
    }
  };

  return (
    <div>
      {/* Your component JSX */}
    </div>
  );
}
```

## Configuration

### Environment Variables

Set the API base URL in your environment file:

```env
# web/.env.local
NEXT_PUBLIC_API_URL=http://localhost:8000/api
```

### Axios Instance Configuration

The API client is configured with:
- Base URL from environment variable
- 10-second timeout
- JSON content type headers
- Request/response interceptors

## Available Endpoints

### Authentication Endpoints
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password with token
- `POST /auth/change-password` - Change password (authenticated)
- `POST /auth/refresh` - Refresh access token
- `GET /auth/me` - Get current user info
- `POST /auth/logout` - Logout user

### Data Endpoints
- `GET /data/users` - Get paginated users list
- `GET /data/users/{id}` - Get specific user
- `GET /data/stats` - Get application statistics
- `GET /data/health` - Health check

## Type Definitions

### Common Types

```typescript
interface User {
  id: number;
  email: string;
  name?: string;
  created_at: string;
  updated_at: string;
}

interface UsersResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

interface AuthResponse {
  user: User;
  tokens: {
    access_token: string;
    refresh_token: string;
    token_type: string;
  };
}
```

## Best Practices

1. **Always use TypeScript types** for API responses
2. **Handle errors gracefully** with try-catch blocks
3. **Use loading states** for better UX
4. **Implement retry logic** for critical operations
5. **Cache responses** when appropriate
6. **Use custom hooks** for reusable API logic

## Development Features

### Request Logging
In development mode, all API requests and responses are logged to the console:
```
🚀 API Request: GET /data/users
✅ API Response: 200 /data/users
```

### Error Logging
API errors are automatically logged with details:
```
❌ API Error: 404 /data/users/999 { detail: "User not found" }
```

This centralized approach ensures consistent API handling across your entire application while providing excellent developer experience and type safety.
