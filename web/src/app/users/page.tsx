import { useState } from "react";

interface User {
    id: null
}

export default function UsersPage() {
    const [users, setUsers] = useState<User[]>([]);]

    return (
        <>
            <h1 className="text-3xl font-bold">Users</h1>
            <table className="table-auto w-full border-2 border-amber-100">
                <thead className="border-b-2 border-amber-100">
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Last Active</th>
                        <th>Joined</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td><PERSON></td>
                        <td><EMAIL></td>
                        <td>2025-01-01 12:00:00</td>
                        <td>2025-01-01 12:00:00</td>
                    </tr>
                </tbody>
            </table>
        </>
    );
}
