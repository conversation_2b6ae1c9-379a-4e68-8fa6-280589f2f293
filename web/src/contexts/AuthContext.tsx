"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { apiClient, ApiError } from '@/lib/api';

// Types
export interface User {
  id: number;
  email: string;
  name?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; message: string }>;
  signup: (email: string, name: string, password: string) => Promise<{ success: boolean; message: string }>;
  logout: () => Promise<{ success: boolean; message: string }>;
  forgotPassword: (email: string) => Promise<{ success: boolean; message: string }>;
  resetPassword: (token: string, newPassword: string) => Promise<{ success: boolean; message: string }>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<{ success: boolean; message: string }>;
  checkAuth: () => Promise<boolean>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Check authentication status on mount using cookies
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Try to get current user - if cookies are valid, this will work
        const userInfo = await getCurrentUser();
        if (userInfo) {
          setUser(userInfo);
        }
      } catch (error) {
        console.log('No valid authentication found');
        // User is not authenticated, which is fine
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  // Helper function to handle API errors
  const handleApiError = (error: any): { success: boolean; message: string } => {
    if (error && typeof error === 'object' && 'message' in error) {
      return { success: false, message: error.message };
    }
    return { success: false, message: error?.toString() || 'An unexpected error occurred' };
  };

  // Get current user info (cookies are sent automatically)
  const getCurrentUser = async (): Promise<User | null> => {
    try {
      const userData = await apiClient.get<User>('/auth/me');
      return userData;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  };

  // Check authentication status
  const checkAuth = async (): Promise<boolean> => {
    try {
      const userInfo = await getCurrentUser();
      if (userInfo) {
        setUser(userInfo);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking auth:', error);
      return false;
    }
  };

  // Login function
  const login = async (email: string, password: string) => {
    try {
      const response = await apiClient.post<{ user: User }>('/auth/signin', {
        email,
        password,
      });

      setUser(response.user);
      return { success: true, message: 'Login successful' };
    } catch (error) {
      return handleApiError(error);
    }
  };

  // Signup function
  const signup = async (email: string, name: string, password: string) => {
    try {
      const response = await apiClient.post<{ user: User }>('/auth/signup', {
        email,
        name,
        password,
      });

      setUser(response.user);
      return { success: true, message: 'Account created successfully' };
    } catch (error) {
      return handleApiError(error);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call backend logout to clear HttpOnly cookies
      await apiClient.post('/auth/logout');
      setUser(null);
      return { success: true, message: 'Logged out successfully' };
    } catch (error) {
      // Even if backend call fails, clear user state
      setUser(null);
      return { success: true, message: 'Logged out successfully' };
    }
  };

  // Forgot password function
  const forgotPassword = async (email: string) => {
    try {
      const response = await apiClient.post<{ message: string }>('/auth/forgot-password', {
        email,
      });

      return { success: true, message: response.message };
    } catch (error) {
      return handleApiError(error);
    }
  };

  // Reset password function
  const resetPassword = async (token: string, newPassword: string) => {
    try {
      const response = await apiClient.post<{ message: string }>('/auth/reset-password', {
        token,
        new_password: newPassword,
      });

      return { success: true, message: response.message };
    } catch (error) {
      return handleApiError(error);
    }
  };

  // Change password function
  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      const response = await apiClient.post<{ message: string }>('/auth/change-password', {
        current_password: currentPassword,
        new_password: newPassword,
      });

      return { success: true, message: response.message };
    } catch (error) {
      return handleApiError(error);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    signup,
    logout,
    forgotPassword,
    resetPassword,
    changePassword,
    checkAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
