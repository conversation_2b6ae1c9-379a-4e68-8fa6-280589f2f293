/**
 * Secure API client using HttpOnly cookies for authentication
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';

// Types for our API responses
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
}

export interface ApiError {
  message: string;
  detail?: string;
  success: boolean;
}

// Function to get auth token from localStorage
const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null;

  try {
    const authTokens = localStorage.getItem('auth_tokens');
    if (authTokens) {
      const tokens = JSON.parse(authTokens);
      return tokens.access_token;
    }
  } catch (error) {
    console.error('Error getting auth token:', error);
  }

  return null;
};

// Function to clear auth tokens
const clearAuthTokens = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_tokens');
  }
};

// Create axios instance with proper header-based authentication
const api: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token to headers
api.interceptors.request.use(
  (config) => {
    const token = getAuthToken();

    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Log request in development
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase() || 'GET'} ${config.url || 'unknown'}`);
    }

    return config;
  },
  (error: AxiosError) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log response in development
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.status} ${response.config?.url || 'unknown'}`);
    }

    return response;
  },
  (error: AxiosError) => {
    // Handle common error scenarios
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data as any;

      // Log error in development
      if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
        console.error(`❌ API Error: ${status} ${error.config?.url || 'unknown'}`, data);
      }

      // Handle authentication errors
      if (status === 401) {
        console.warn('🚨 Authentication failed (401), clearing tokens');
        console.warn('🚨 Failed request:', error.config?.url);
        console.warn('🚨 Response data:', data);
        clearAuthTokens();

        // Redirect to login if not already on auth page
        if (typeof window !== 'undefined' && !window.location.pathname.startsWith('/auth')) {
          console.warn('🚨 Redirecting to login page');
          window.location.href = '/auth/login';
        }
      }

      // Handle server errors
      if (status >= 500) {
        console.error('Server error occurred');
      }

      // Return formatted error
      const apiError: ApiError = {
        message: data?.detail || data?.message || 'An error occurred',
        detail: data?.detail,
        success: false,
      };

      return Promise.reject(apiError);
    } else if (error.request) {
      // Network error
      const networkError: ApiError = {
        message: 'Network error - please check your connection',
        success: false,
      };

      console.error('Network error:', error.request);
      return Promise.reject(networkError);
    } else {
      // Other error
      const otherError: ApiError = {
        message: error.message || 'An unexpected error occurred',
        success: false,
      };

      console.error('API setup error:', error.message);
      return Promise.reject(otherError);
    }
  }
);

// Convenience methods for common HTTP operations
export const apiClient = {
  // GET request
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.get<T>(url, config);
    return response.data;
  },

  // POST request
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.post<T>(url, data, config);
    return response.data;
  },

  // PUT request
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.put<T>(url, data, config);
    return response.data;
  },

  // PATCH request
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.patch<T>(url, data, config);
    return response.data;
  },

  // DELETE request
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await api.delete<T>(url, config);
    return response.data;
  },
};

// Export the configured axios instance for direct use if needed
export default api;
