import axios from 'axios'

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL, // e.g., http://localhost:8000/api
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // for cookie-based auth, can remove if not needed
})

// Add auth token automatically if it exists in localStorage
api.interceptors.request.use(
  (config) => {
    const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null
    if (token && config.headers) {
        console.log('Adding token to headers:', token)
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

export default api
