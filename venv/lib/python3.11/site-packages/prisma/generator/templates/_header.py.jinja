{% include '_pragma.py.jinja' %}
# pyright: reportUnusedImport=false
# fmt: off
{% if annotations is defined and annotations %}
from __future__ import annotations
{% else %}
{% endif %}

# global imports for type checking
from builtins import bool as _bool
from builtins import int as _int
from builtins import float as _float
from builtins import str as _str
import sys
import decimal
import datetime
from typing import (
    TYPE_CHECKING,
    Optional,
    Iterable,
    Iterator,
    Sequence,
    Callable,
    ClassVar,
    NoReturn,
    TypeVar,
    Generic,
    Mapping,
    Tuple,
    Union,
    List,
    Dict,
    Type,
    Any,
    Set,
    overload,
    cast,
)
from typing_extensions import TypedDict, Literal

{% from '_utils.py.jinja' import recursive_types with context %}

{% if recursive_types %}
from typing_extensions import LiteralString
{% else %}
LiteralString = str
{% endif %}

