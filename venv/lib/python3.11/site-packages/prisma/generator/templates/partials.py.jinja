{% include '_header.py.jinja' %}
# -- template partials.py.jinja --
from . import types, fields, enums, actions, models, bases
from ._compat import model_rebuild, field_validator


{% for partial in partial_models %}
class {{ partial.name }}(bases.Base{{ partial.from_model }}):
    {% for field in partial.fields.values() %}
    {{ field.name }}:
            {%- if field.optional -%}
                {{ ' ' }}Optional[{{ field.type }}] = None
            {% else -%}
                {{ ' ' }}{{ field.type }}
            {% endif %}
    {% if not field.documentation is none %}
    """{{ format_documentation(field.documentation) }}"""

    {% endif %}
    {% endfor %}

    {% for field in partial.fields.values() %}
    {% if field.is_list and not field.is_relational and not field.optional %}
    @field_validator('{{ field.name }}', pre=True, allow_reuse=True)
    @classmethod
    def _transform_required_{{ field.name }}_list(cls, value: object) -> object:
        # See the `_transform_required_list_fields()` function in `prisma/models.py` for context
        if value is None:
            return []

        return value
    {% endif %}
    {% endfor %}


{% endfor %}

# users can modify relational types which are then namespaced to partials.
# so we have to import ourselves in order to resolve forward references
from . import partials

{% for partial in partial_models %}
model_rebuild({{ partial.name }})
{% endfor %}

# fmt: on

