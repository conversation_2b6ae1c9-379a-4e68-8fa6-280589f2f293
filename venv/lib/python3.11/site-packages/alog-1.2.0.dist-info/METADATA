Metadata-Version: 2.1
Name: alog
Version: 1.2.0
Summary: Your goto Python logging without panic on context swtich
Home-page: https://github.com/keitheis/alog
Author: <PERSON>
Author-email: <EMAIL>
License: Apache 2.0
Keywords: simple basic application logging print
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Topic :: System :: Logging
Classifier: Topic :: Software Development
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Description-Content-Type: text/x-rst
License-File: LICENSE
Provides-Extra: docs
Requires-Dist: Sphinx (>=1.3.1) ; extra == 'docs'
Provides-Extra: testing
Requires-Dist: pytest-cov ; extra == 'testing'

Alog
====

.. image:: https://travis-ci.com/keitheis/alog.svg?branch=master
  :target: https://travis-ci.com/keitheis/alog

.. image:: https://codecov.io/gh/keitheis/alog/branch/master/graph/badge.svg
  :target: https://codecov.io/gh/keitheis/alog

.. image:: http://img.shields.io/pypi/v/alog.svg?style=flat
   :target: https://pypi.org/pypi/alog

Your goto Python logging without panic on context swtich.

**Warning:** No more ``logger = logging.getLogger(__name__)`` in your every file.

.. code-block:: python

  >>> import alog
  >>> alog.info("Hi.")
  2016-12-18 20:44:30 INFO  <stdin> Hi.
  >>> def test():
  ...     alog.info("Test 1")
  ...     alog.error("Test 2")
  ...
  >>> test()
  2016-12-18 20:45:19 INFO  <stdin:2> Test 1
  2016-12-18 20:45:19 ERROR <stdin:3> Test 2
  >>> alog.set_level("ERROR")
  >>> test()
  2016-12-18 20:45:41 ERROR <stdin:3> Test 2

If you're new to logging, see `Why should you use logging instead of print`_.

Installation
------------

.. code-block::

  pip install alog

Features 
--------

- Instant logging with expected defaults.

  You can do logging instantly by reading a small piece of README.
  Alog comes with useful defaults:

  - A default logger.
  - Logging level: ``logging.INFO``
  - Logging format::

    "%(asctime)s %(levelname)-5.5s [parent_module.current_module:%(lineno)s]%(message)s",
    "%Y-%m-%d %H:%M:%S"

- No more **__name__** whenever you start to do logging in a module.

  Alog builds the default module names on the fly. 

- Compatible with default Python ``logging`` module.

  Alog is built upon default Python logging module. You can configure it by
  the same way of default Python logging module when it's needed.


Comparing ``alog`` with Python default ``logging`` module
---------------------------------------------------------

Comparing ``alog`` :

.. code-block:: python

    In [1]: import alog

    In [2]: alog.info("Hello alog!")
    2016-11-23 12:20:34 INFO  <IPython> Hello alog!

with ``logging`` module:

.. code-block:: python

    In [1]: import logging

    In [2]: logging.basicConfig(
       ...:     level=logging.INFO,
       ...:     format="%(asctime)s %(levelname)-5.5s "
       ...:            "[%(name)s:%(lineno)s] %(message)s")

    In [3]: # In every file you want to do log, otherwise %(names)s won't work.
    In [4]: logger = logging.getLogger(__name__)

    In [5]: logger.info("Hello log!")
    2016-11-23 12:16:30 INFO  [__main__:1] Hello log!


Tips
----

.. code-block:: python

    import alog

    a_complex_json_dict = {...}  # or a_complex_dict
    alog.info(alog.pformat(a_complex_dict))

    restaurant = Restaurant(...)
    alog.info(alog.pdir(restaurant))
    # or just skip attributes starts with "__":
    alog.info(alog.pdir(restaurant, str_not_startswith="__"))
    # instead of
    alog.info([attr for attr in dir(restaurant) if attr.startswith("_")])

    # Play threads?
    alog.turn_logging_thread_name(on=True)
    # Processes?
    alog.turn_logging_process_id(on=True)
    # No datetime wanted?
    alog.turn_logging_datetime(on=False)

Why should you use logging instead of print
-------------------------------------------

The main goal of logging is to figure out what was going on and to get the
insight. ``print``, by default, does simply pure string output. No timestamp,
no module hint, and no level control, comparing to a pretty logging record.

Lets start with ``aproject/models/user.py`` :

.. code-block:: python

  class User:
      def __init__(self, user_id, username):
          ...
          print(username)
          ...

What you got output of ``print`` :

.. code-block:: python

  >>> admin = User(1, "admin")
  "admin"


Now use ``alog`` :

.. code-block:: python

  import alog

  class User:
      def __init__(self, user_id, username):
          ...
          alog.info(username)
          ...

What you got output of ``alog.info`` :

.. code-block:: python

  >>> admin = User(1, "admin")
  2016-11-23 11:32:58 INFO  [models.user:6] admin

In the output of hundreds of lines, it helps (a lot).

What if you have used ``print`` a log? That's as easy:

.. code-block:: python

  import alog

  print = alog.info

  ... # A lot of print code no needed to change

1.2.0 (2021-08-09)
==================

 - Support Python 3.9
 - Remove Python 3.4 and 3.5 support.

1.1.0 (2020-02-10)
==================

 - Support Python 3.8
 - Fix broken ``set_format`` function when formatter argument is given.

1.0.0 (2019-04-03)
==================

 - Renamed:

   - ``turn_logging_datetime(on=True)``
   - ``turn_logging_thread_name(on=False)``
   - ``turn_logging_process_id(on=False)``

 - Support most same APIs between alog and Alogger.
 - Add ``alog.pdir()`` for handy replacing ``[attr for attr in dir(obj)
   if not attr.startswith("_")]``.

0.9.13 (2017-06-18)
===================

 - Fix not able to ``turn_log_datetime(on=False)``.

0.9.12 (2017-06-16)
===================

 - Support not showing_log_datetime by ``turn_log_datetime(on=False)``.

0.9.11 (2017-04-07)
===================

 - Add ``alog.getLogger()`` for handy replacing ``logging.getLogger``.

0.9.10 (2017-03-27)
===================

 - Default logging format asctime to "%Y-%m-%d %H:%M:%S" instead of
   "%Y-%m-%d,%H:%M:%S.%f".
 - Update package info and usage (setup.py, README, ...).

0.9.9 (2016-08-28)
==================

 - Update to turn_thread_name and turn_process_id.

0.9.8 (2016-08-27)
==================

 - Support showing_thread_name and showing_process_id.
 - Support global reset.

0.9.7 (2016-08-17)
==================

 - Better paths log for None default root name.

0.9.6 (2016-08-16)
==================

 - First public release.


