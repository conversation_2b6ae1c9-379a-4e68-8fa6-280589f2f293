"use client";

import { useRef, useState } from "react";

export default function ChatPage() {
    const [messages, setMessages] = useState([]);
    const ws = useRef<WebSocket | null>(null);
  const [input, setInput] = useState("");

  const websocket = new WebSocket("ws://127.0.0.1:8000/ws/1");

  websocket.onmessage = (event) => {
    const data = JSON.parse(event.data);
    setMessages((prev) => [
      ...prev,
      { id: Date.now() + 1, text: data.text, sender: "bot" },
    ]);
  };

  const sendMessage = () => {
    if (!input.trim()) return;
    setMessages([...messages, { id: Date.now(), text: input, sender: "user" }]);
    setInput("");

    websocket.send(JSON.stringify({ text: input }));

    // Simulated response (you can replace with backend call)
    // setTimeout(() => {
    //   setMessages((prev) => [
    //     ...prev,
    //     { id: Date.now() + 1, text: "Got it!", sender: "bot" },
    //   ]);
    // }, 800);
  };

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)] max-w-2xl mx-auto bg-white border rounded-md shadow mt-4">
      {/* Chat messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`max-w-xs px-4 py-2 rounded-lg text-sm ${
              msg.sender === "user"
                ? "ml-auto bg-blue-500 text-white"
                : "mr-auto bg-gray-200 text-gray-800"
            }`}
          >
            {msg.text}
          </div>
        ))}
      </div>

      {/* Input box */}
      <div className="border-t p-4 flex items-center gap-2">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Type your message..."
          className="flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          onKeyDown={(e) => e.key === "Enter" && sendMessage()}
        />
        <button
          onClick={sendMessage}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Send
        </button>
      </div>
    </div>
  );
}
