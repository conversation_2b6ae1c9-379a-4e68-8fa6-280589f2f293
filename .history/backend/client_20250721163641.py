import asyncio, websockets

async def connect():
    uri = "ws://localhost:8000/ws/1"
    async with websockets.connect(uri) as websocket:
        print("Connected to websocket server")

        await websocket.send("Hello from client!")
        print("Message sent to server")

        message = await websocket.recv()
        print(f"Received message from server: {message}")

asyncio.get_event_loop().run_until_complete(connect())
