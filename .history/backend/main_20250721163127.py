from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from backend.connection import manager

app = FastAPI()

@app.get('/')
async def root():
    return {"message": "Works!"}

@app.websocket("/ws/{client_id}")
async def chat_websocket(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            await manager.broadcast(data)
    except WebSocketDisconnect:
        manager.disconnect(websocket)
