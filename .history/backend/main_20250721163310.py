from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from backend.connection import manager
import alog

app = FastAPI()

@app.get('/')
async def root():
    return {"message": "Works!"}

@app.websocket("/ws/{client_id}")
async def chat_websocket(websocket: WebSocket):
    await manager.connect(websocket)
    client_id = websocket.path_params["client_id"]
    alog.info(f"Client ID: {client_id}")
    try:
        while True:
            data = await websocket.receive_text()
            await manager.broadcast(data)
    except WebSocketDisconnect:
        manager.disconnect(websocket)
